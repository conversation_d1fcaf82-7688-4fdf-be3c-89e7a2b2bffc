"use client";

import { useState } from "react";
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Loader2, AlertCircle, Copy, Check } from "lucide-react";
import { toast } from "sonner";
import { validateUsername, validatePassword } from "@/lib/validation";

interface AdminAuthDialogProps {
  open: boolean;
  onClose: () => void;
  onSuccess: () => void;
}

export function AdminAuthDialog({
  open,
  onClose,
  onSuccess,
}: AdminAuthDialogProps) {
  const [activeTab, setActiveTab] = useState("signin");
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState("");
  const [generatedRecoveryKey, setGeneratedRecoveryKey] = useState("");
  const [copied, setCopied] = useState(false);

  // Form states for admin users only
  const [signInData, setSignInData] = useState({
    username: "",
    password: "",
  });
  const [signUpData, setSignUpData] = useState({
    username: "",
    password: "",
    recoveryKey: "",
  });
  const [forgotPasswordData, setForgotPasswordData] = useState({
    username: "",
    recoveryKey: "",
    newPassword: "",
  });

  const resetForms = () => {
    setSignInData({ username: "", password: "" });
    setSignUpData({ username: "", password: "", recoveryKey: "" });
    setForgotPasswordData({ username: "", recoveryKey: "", newPassword: "" });
    setError("");
    setGeneratedRecoveryKey("");
    setCopied(false);
  };

  const copyRecoveryKey = async () => {
    try {
      await navigator.clipboard.writeText(generatedRecoveryKey);
      setCopied(true);
      toast.success("Recovery key copied to clipboard!");
      setTimeout(() => setCopied(false), 2000);
    } catch (err) {
      toast.error("Failed to copy recovery key");
    }
  };

  const handleClose = () => {
    resetForms();
    onClose();
  };

  const handleSignIn = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);
    setError("");

    // Client-side validation
    const usernameValidation = validateUsername(signInData.username);
    if (!usernameValidation.isValid) {
      setError(usernameValidation.message!);
      setLoading(false);
      return;
    }

    const passwordValidation = validatePassword(signInData.password);
    if (!passwordValidation.isValid) {
      setError(passwordValidation.message!);
      setLoading(false);
      return;
    }

    try {
      const response = await fetch("/api/auth/signin", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({
          username: signInData.username,
          password: signInData.password,
        }),
      });

      const data = await response.json();

      if (response.ok) {
        // Only authenticate admin users through this dialog
        if (data.user.role === "admin") {
          toast.success("Admin signed in successfully!");
          onSuccess();
          resetForms();
        } else {
          setError("Only admin users can authenticate through this dialog.");
        }
      } else {
        toast.error(data.error || "Sign in failed");
        setError(data.error || "Sign in failed");
      }
    } catch {
      toast.error("Network error. Please try again.");
      setError("Network error. Please try again.");
    } finally {
      setLoading(false);
    }
  };

  const handleSignUp = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);
    setError("");

    // Client-side validation
    const usernameValidation = validateUsername(signUpData.username);
    if (!usernameValidation.isValid) {
      setError(usernameValidation.message!);
      setLoading(false);
      return;
    }

    const passwordValidation = validatePassword(signUpData.password);
    if (!passwordValidation.isValid) {
      setError(passwordValidation.message!);
      setLoading(false);
      return;
    }

    try {
      const response = await fetch("/api/auth/signup", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({
          username: signUpData.username,
          password: signUpData.password,
          role: "admin", // Always admin for this dialog
          recoveryKey: signUpData.recoveryKey || undefined,
        }),
      });

      const data = await response.json();

      if (response.ok) {
        if (data.recoveryKey) {
          setGeneratedRecoveryKey(data.recoveryKey);
        }

        toast.success("Admin account created successfully!");
        // Don't call onSuccess or reset forms immediately if recovery key was generated - let user see it first
        if (!data.recoveryKey) {
          onSuccess();
          resetForms();
        }
      } else {
        toast.error(data.error || "Sign up failed");
        setError(data.error || "Sign up failed");
      }
    } catch {
      toast.error("Network error. Please try again.");
      setError("Network error. Please try again.");
    } finally {
      setLoading(false);
    }
  };

  const handleForgotPassword = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);
    setError("");

    // Validate all fields
    const usernameValidation = validateUsername(forgotPasswordData.username);
    if (!usernameValidation.isValid) {
      setError(usernameValidation.message!);
      setLoading(false);
      return;
    }

    if (!forgotPasswordData.recoveryKey) {
      setError("Recovery key is required for admin password reset");
      setLoading(false);
      return;
    }

    const passwordValidation = validatePassword(forgotPasswordData.newPassword);
    if (!passwordValidation.isValid) {
      setError(passwordValidation.message!);
      setLoading(false);
      return;
    }

    try {
      const response = await fetch("/api/auth/forgot-password", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({
          username: forgotPasswordData.username,
          recoveryKey: forgotPasswordData.recoveryKey,
          newPassword: forgotPasswordData.newPassword,
        }),
      });

      const data = await response.json();

      if (response.ok) {
        toast.success("Admin password reset successfully!");
        setActiveTab("signin");
        resetForms();
      } else {
        toast.error(data.error || "Password reset failed");
        setError(data.error || "Password reset failed");
      }
    } catch {
      toast.error("Network error. Please try again.");
      setError("Network error. Please try again.");
    } finally {
      setLoading(false);
    }
  };

  return (
    <Dialog open={open} onOpenChange={handleClose}>
      <DialogContent className="sm:max-w-[425px]">
        <DialogHeader>
          <DialogTitle>Admin Authentication</DialogTitle>
          <DialogDescription>
            Sign in as an administrator to access admin mode and administrative
            features.
          </DialogDescription>
        </DialogHeader>

        {error && (
          <Alert variant="destructive">
            <AlertCircle className="h-4 w-4" />
            <AlertDescription>{error}</AlertDescription>
          </Alert>
        )}

        {generatedRecoveryKey && (
          <Alert>
            <AlertCircle className="h-4 w-4" />
            <AlertDescription>
              <div className="space-y-3">
                <p className="font-medium">
                  Your admin recovery key has been generated:
                </p>
                <div className="relative">
                  <code className="block p-2 pr-12 bg-muted rounded text-sm break-all">
                    {generatedRecoveryKey}
                  </code>
                  <Button
                    size="sm"
                    variant="ghost"
                    onClick={copyRecoveryKey}
                    className="absolute right-1 top-1 h-8 w-8 p-0"
                  >
                    {copied ? (
                      <Check className="h-4 w-4 text-green-600" />
                    ) : (
                      <Copy className="h-4 w-4" />
                    )}
                  </Button>
                </div>
                <p className="text-xs">
                  Please save this recovery key in a secure location. You'll
                  need it to recover your admin account if you forget your
                  password.
                </p>
                <Button
                  size="sm"
                  variant="outline"
                  onClick={copyRecoveryKey}
                  className="w-full"
                >
                  {copied ? (
                    <>
                      <Check className="mr-2 h-4 w-4 text-green-600" />
                      Copied!
                    </>
                  ) : (
                    <>
                      <Copy className="mr-2 h-4 w-4" />
                      Copy Key
                    </>
                  )}
                </Button>
              </div>
            </AlertDescription>
          </Alert>
        )}

        <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
          <TabsList className="grid w-full grid-cols-3">
            <TabsTrigger value="signin">Sign In</TabsTrigger>
            <TabsTrigger value="signup">Sign Up</TabsTrigger>
            <TabsTrigger value="forgot">Forgot Password</TabsTrigger>
          </TabsList>

          <TabsContent value="signin" className="space-y-4">
            <form onSubmit={handleSignIn} className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="admin-signin-username">Username</Label>
                <Input
                  id="admin-signin-username"
                  type="text"
                  value={signInData.username}
                  onChange={(e) =>
                    setSignInData({ ...signInData, username: e.target.value })
                  }
                  required
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="admin-signin-password">Password</Label>
                <Input
                  id="admin-signin-password"
                  type="password"
                  value={signInData.password}
                  onChange={(e) =>
                    setSignInData({ ...signInData, password: e.target.value })
                  }
                  required
                />
              </div>
              <Button type="submit" className="w-full" disabled={loading}>
                {loading && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
                Sign In as Admin
              </Button>
            </form>
          </TabsContent>

          <TabsContent value="signup" className="space-y-4">
            <form onSubmit={handleSignUp} className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="admin-signup-username">Username</Label>
                <Input
                  id="admin-signup-username"
                  type="text"
                  value={signUpData.username}
                  onChange={(e) =>
                    setSignUpData({ ...signUpData, username: e.target.value })
                  }
                  required
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="admin-signup-password">Password</Label>
                <Input
                  id="admin-signup-password"
                  type="password"
                  value={signUpData.password}
                  onChange={(e) =>
                    setSignUpData({ ...signUpData, password: e.target.value })
                  }
                  required
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="admin-signup-recovery">
                  Recovery Key (Optional)
                </Label>
                <Input
                  id="admin-signup-recovery"
                  type="text"
                  value={signUpData.recoveryKey}
                  onChange={(e) =>
                    setSignUpData({
                      ...signUpData,
                      recoveryKey: e.target.value,
                    })
                  }
                  placeholder="Leave empty to auto-generate"
                />
                <p className="text-xs text-muted-foreground">
                  If left empty, a recovery key will be automatically generated
                  for you. This key is required for password recovery.
                </p>
              </div>
              <Button
                type={generatedRecoveryKey ? "button" : "submit"}
                className="w-full"
                disabled={loading}
                onClick={
                  generatedRecoveryKey
                    ? () => {
                        setGeneratedRecoveryKey("");
                        resetForms();
                        onSuccess();
                      }
                    : undefined
                }
              >
                {loading && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
                {generatedRecoveryKey ? "Done" : "Create Admin Account"}
              </Button>
            </form>
          </TabsContent>

          <TabsContent value="forgot" className="space-y-4">
            <form onSubmit={handleForgotPassword} className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="admin-forgot-username">Username</Label>
                <Input
                  id="admin-forgot-username"
                  type="text"
                  value={forgotPasswordData.username}
                  onChange={(e) =>
                    setForgotPasswordData({
                      ...forgotPasswordData,
                      username: e.target.value,
                    })
                  }
                  required
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="admin-forgot-recovery">Recovery Key</Label>
                <Input
                  id="admin-forgot-recovery"
                  type="text"
                  value={forgotPasswordData.recoveryKey}
                  onChange={(e) =>
                    setForgotPasswordData({
                      ...forgotPasswordData,
                      recoveryKey: e.target.value,
                    })
                  }
                  placeholder="Enter your recovery key"
                  required
                />
                <p className="text-xs text-muted-foreground">
                  Enter the recovery key that was provided when you created your
                  admin account.
                </p>
              </div>
              <div className="space-y-2">
                <Label htmlFor="admin-forgot-new-password">New Password</Label>
                <Input
                  id="admin-forgot-new-password"
                  type="password"
                  value={forgotPasswordData.newPassword}
                  onChange={(e) =>
                    setForgotPasswordData({
                      ...forgotPasswordData,
                      newPassword: e.target.value,
                    })
                  }
                  required
                />
              </div>
              <Button type="submit" className="w-full" disabled={loading}>
                {loading && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
                Reset Admin Password
              </Button>
            </form>
          </TabsContent>
        </Tabs>
      </DialogContent>
    </Dialog>
  );
}
